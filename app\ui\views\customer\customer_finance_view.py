"""
Vue pour gérer les finances des clients.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView, QHeaderView,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame, QMessageBox,
    QTabWidget, QSplitter, QDateEdit
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer, pyqtSlot, QDate
from PyQt6.QtGui import QIcon
import asyncio
from datetime import datetime, timedelta

from app.core.services.customer_service import CustomerService
from app.core.services.sale_service import SaleService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay
from app.ui.models.customer_finance_table_model import CustomerTransactionTableModel, CustomerSaleTableModel
from app.ui.models.customer_repair_table_model import CustomerRepairTableModel
from .dialogs.financial_dialog import FinancialDialog
from .widgets.customer_balance_widget import CustomerBalanceWidget

class CustomerFinanceView(QWidget):
    """Vue pour gérer les finances des clients"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = CustomerService(self.db)
        self.sale_service = SaleService(self.db)

        # Service de réparation
        from app.core.services.repair_service import RepairService
        self.repair_service = RepairService(self.db)

        # Configuration de l'interface
        self.setup_ui()

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        # Timer pour rafraîchir automatiquement le widget de solde
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_balance_widget)
        self.refresh_timer.start(30000)  # Rafraîchir toutes les 30 secondes

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        title_label = QLabel("Finances clients")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Splitter principal
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Panneau de gauche (liste des clients et solde)
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Filtre des clients
        filter_layout = QHBoxLayout()

        self.customer_filter = QLineEdit()
        self.customer_filter.setPlaceholderText("Rechercher un client...")
        self.customer_filter.textChanged.connect(self.filter_customers)
        filter_layout.addWidget(self.customer_filter)

        left_layout.addLayout(filter_layout)

        # Liste des clients
        self.customer_combo = QComboBox()
        self.customer_combo.currentIndexChanged.connect(self.on_customer_changed)
        left_layout.addWidget(self.customer_combo)

        # Widget de solde client
        self.balance_widget = CustomerBalanceWidget()
        left_layout.addWidget(self.balance_widget)

        # Boutons d'action
        action_layout = QHBoxLayout()

        self.new_transaction_button = QPushButton("Nouvelle transaction")
        self.new_transaction_button.setIcon(QIcon("app/ui/resources/icons/transaction.svg"))
        self.new_transaction_button.clicked.connect(self.show_new_transaction_dialog)
        action_layout.addWidget(self.new_transaction_button)

        self.new_payment_button = QPushButton("Nouveau paiement")
        self.new_payment_button.setIcon(QIcon("app/ui/resources/icons/payment.svg"))
        self.new_payment_button.clicked.connect(self.show_new_payment_dialog)
        action_layout.addWidget(self.new_payment_button)

        left_layout.addLayout(action_layout)

        # Ajouter le panneau de gauche au splitter
        main_splitter.addWidget(left_widget)

        # Panneau de droite (onglets transactions et ventes)
        right_widget = QTabWidget()

        # Onglet transactions
        transactions_tab = QWidget()
        transactions_layout = QVBoxLayout(transactions_tab)

        # Filtre des transactions
        transaction_filter_layout = QHBoxLayout()

        self.transaction_filter = QLineEdit()
        self.transaction_filter.setPlaceholderText("Rechercher une transaction...")
        self.transaction_filter.textChanged.connect(self.filter_transactions)
        transaction_filter_layout.addWidget(self.transaction_filter)

        self.date_filter = QDateEdit()
        self.date_filter.setDate(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        self.date_filter.dateChanged.connect(self.filter_transactions)
        transaction_filter_layout.addWidget(self.date_filter)

        transactions_layout.addLayout(transaction_filter_layout)

        # Tableau des transactions
        self.transactions_table = QTableView()
        self.transactions_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.transactions_table.setAlternatingRowColors(True)

        # Modèle de données pour les transactions
        self.transactions_model = CustomerTransactionTableModel()
        self.transactions_proxy_model = QSortFilterProxyModel()
        self.transactions_proxy_model.setSourceModel(self.transactions_model)
        self.transactions_proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.transactions_table.setModel(self.transactions_proxy_model)

        # Configuration avancée du tableau pour une meilleure présentation
        header = self.transactions_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.transactions_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.transactions_table.setShowGrid(True)
        self.transactions_table.setSortingEnabled(True)

        transactions_layout.addWidget(self.transactions_table)

        # Ajouter l'onglet transactions
        right_widget.addTab(transactions_tab, "Transactions")

        # Onglet ventes
        sales_tab = QWidget()
        sales_layout = QVBoxLayout(sales_tab)

        # Filtre des ventes
        sales_filter_layout = QHBoxLayout()

        self.sales_filter = QLineEdit()
        self.sales_filter.setPlaceholderText("Rechercher une vente...")
        self.sales_filter.textChanged.connect(self.filter_sales)
        sales_filter_layout.addWidget(self.sales_filter)

        self.payment_status_filter = QComboBox()
        self.payment_status_filter.addItem("Tous les statuts", None)
        self.payment_status_filter.addItem("En attente", "pending")
        self.payment_status_filter.addItem("Partiellement payé", "partial")
        self.payment_status_filter.addItem("Payé", "paid")
        self.payment_status_filter.addItem("En retard", "overdue")
        self.payment_status_filter.currentIndexChanged.connect(self.filter_sales)
        sales_filter_layout.addWidget(self.payment_status_filter)

        sales_layout.addLayout(sales_filter_layout)

        # Tableau des ventes
        self.sales_table = QTableView()
        self.sales_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.sales_table.setAlternatingRowColors(True)

        # Modèle de données pour les ventes
        self.sales_model = CustomerSaleTableModel()
        self.sales_proxy_model = QSortFilterProxyModel()
        self.sales_proxy_model.setSourceModel(self.sales_model)
        self.sales_proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.sales_table.setModel(self.sales_proxy_model)

        # Configuration avancée du tableau pour une meilleure présentation
        header = self.sales_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.sales_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.sales_table.setShowGrid(True)
        self.sales_table.setSortingEnabled(True)

        sales_layout.addWidget(self.sales_table)

        # Ajouter l'onglet ventes
        right_widget.addTab(sales_tab, "Ventes")

        # Onglet réparations
        repairs_tab = QWidget()
        repairs_layout = QVBoxLayout(repairs_tab)

        # Filtre des réparations
        repairs_filter_layout = QHBoxLayout()

        self.repairs_filter = QLineEdit()
        self.repairs_filter.setPlaceholderText("Rechercher une réparation...")
        self.repairs_filter.textChanged.connect(self.filter_repairs)
        repairs_filter_layout.addWidget(self.repairs_filter)

        self.repair_status_filter = QComboBox()
        self.repair_status_filter.addItem("Tous les statuts", None)
        self.repair_status_filter.addItem("En attente", "pending")
        self.repair_status_filter.addItem("En cours", "in_progress")
        self.repair_status_filter.addItem("Terminé", "completed")
        self.repair_status_filter.addItem("Facturé", "invoiced")
        self.repair_status_filter.addItem("Payé", "paid")
        self.repair_status_filter.currentIndexChanged.connect(self.filter_repairs)
        repairs_filter_layout.addWidget(self.repair_status_filter)

        repairs_layout.addLayout(repairs_filter_layout)

        # Tableau des réparations
        self.repairs_table = QTableView()
        self.repairs_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.repairs_table.setAlternatingRowColors(True)

        # Modèle de données pour les réparations
        self.repairs_model = CustomerRepairTableModel()
        self.repairs_proxy_model = QSortFilterProxyModel()
        self.repairs_proxy_model.setSourceModel(self.repairs_model)
        self.repairs_proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.repairs_table.setModel(self.repairs_proxy_model)

        # Configuration avancée du tableau pour une meilleure présentation
        header = self.repairs_table.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.repairs_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.repairs_table.setShowGrid(True)
        self.repairs_table.setSortingEnabled(True)

        repairs_layout.addWidget(self.repairs_table)

        # Ajouter des boutons d'action pour les réparations
        repairs_actions_layout = QHBoxLayout()

        self.view_repair_button = QPushButton("Voir détails")
        self.view_repair_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))  # Utiliser une icône existante
        self.view_repair_button.clicked.connect(self.show_repair_details)
        repairs_actions_layout.addWidget(self.view_repair_button)

        self.invoice_repair_button = QPushButton("Facturer")
        self.invoice_repair_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))  # Utiliser une icône existante
        self.invoice_repair_button.clicked.connect(self.show_invoice_dialog)
        repairs_actions_layout.addWidget(self.invoice_repair_button)

        self.payment_repair_button = QPushButton("Paiement")
        self.payment_repair_button.setIcon(QIcon("app/ui/resources/icons/finance.svg"))  # Utiliser une icône existante
        self.payment_repair_button.clicked.connect(self.show_repair_payment_dialog)
        repairs_actions_layout.addWidget(self.payment_repair_button)

        repairs_layout.addLayout(repairs_actions_layout)

        # Ajouter l'onglet réparations
        right_widget.addTab(repairs_tab, "Réparations")

        # Ajouter le panneau de droite au splitter
        main_splitter.addWidget(right_widget)

        # Configurer le splitter
        main_splitter.setStretchFactor(0, 1)
        main_splitter.setStretchFactor(1, 3)

        # Ajouter le splitter au layout principal
        main_layout.addWidget(main_splitter)

    def load_data(self):
        """Charge les données"""
        self.loading_overlay.show()

        # Utiliser QTimer pour exécuter le chargement de manière asynchrone
        QTimer.singleShot(0, self._load_data_wrapper)

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data_async de manière asynchrone"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la coroutine dans la boucle
            loop.run_until_complete(self._load_data_async())

            # Fermer la boucle après utilisation
            loop.close()

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    async def _load_data_async(self):
        """Charge les données de manière asynchrone"""
        try:
            # Charger les clients
            customers = await self.service.get_active_customers()

            # Remplir le combo des clients
            self.customer_combo.clear()
            self.customer_combo.addItem("Tous les clients", None)

            for customer in customers:
                self.customer_combo.addItem(customer.name, customer.id)

            # Charger les transactions
            transactions = await self.service.get_all_transactions()
            self.transactions_model.set_data(transactions)

            # Charger les ventes
            sales = await self.sale_service.get_all()
            self.sales_model.set_data(sales)

            # Charger les réparations
            repairs = await self.repair_service.get_all()
            self.repairs_model.set_data(repairs)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Erreur lors du chargement des données: {str(e)}"
            )
        finally:
            self.loading_overlay.hide()

    def filter_customers(self):
        """Filtre la liste des clients"""
        filter_text = self.customer_filter.text().lower()

        # QComboBox n'a pas de méthode setItemVisible, nous devons donc
        # recréer le combo avec les éléments filtrés
        current_id = self.customer_combo.currentData()

        # Sauvegarder les éléments actuels
        items = []
        for i in range(self.customer_combo.count()):
            item_text = self.customer_combo.itemText(i)
            item_data = self.customer_combo.itemData(i)
            items.append((item_text, item_data))

        # Effacer le combo
        self.customer_combo.clear()

        # Ajouter les éléments filtrés
        for item_text, item_data in items:
            if filter_text in item_text.lower():
                self.customer_combo.addItem(item_text, item_data)

        # Restaurer la sélection précédente si possible
        if current_id is not None:
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == current_id:
                    self.customer_combo.setCurrentIndex(i)
                    break

    def filter_transactions(self):
        """Filtre la liste des transactions"""
        filter_text = self.transaction_filter.text()
        self.transactions_proxy_model.setFilterFixedString(filter_text)

        # Filtrer par date
        date = self.date_filter.date().toPyDate()
        self.transactions_model.filter_by_date(date)

    def filter_sales(self):
        """Filtre la liste des ventes"""
        filter_text = self.sales_filter.text()
        self.sales_proxy_model.setFilterFixedString(filter_text)

        # Filtrer par statut de paiement
        status = self.payment_status_filter.currentData()
        if status:
            self.sales_model.filter_by_payment_status(status)
        else:
            self.sales_model.clear_payment_status_filter()

    def on_customer_changed(self, index):
        """Gère le changement de client"""
        customer_id = self.customer_combo.currentData()

        # Filtrer les transactions, ventes et réparations par client
        self.transactions_model.filter_by_customer(customer_id)
        self.sales_model.filter_by_customer(customer_id)
        self.repairs_model.filter_by_customer(customer_id)

        # Mettre à jour le widget de solde
        if customer_id:
            # Forcer la recréation de la session dans le widget de solde
            if hasattr(self.balance_widget, 'db') and self.balance_widget.db:
                self.balance_widget.db.close()

            from app.utils.database import SessionLocal
            self.balance_widget.db = SessionLocal()
            self.balance_widget.service = CustomerService(self.balance_widget.db)

            self.balance_widget.set_customer(customer_id)
        else:
            self.balance_widget.clear()

    def show_new_transaction_dialog(self):
        """Affiche la boîte de dialogue de nouvelle transaction"""
        customer_id = self.customer_combo.currentData()
        if not customer_id:
            QMessageBox.warning(
                self,
                "Attention",
                "Veuillez sélectionner un client avant de créer une transaction."
            )
            return

        dialog = FinancialDialog(self, customer_id=customer_id)
        if dialog.exec():
            self._load_data_wrapper()

            # Rafraîchir explicitement le widget de solde
            print(f"Rafraîchissement du solde client après nouvelle transaction pour le client {customer_id}")
            # Forcer la recréation de la session dans le widget de solde
            if hasattr(self.balance_widget, 'db') and self.balance_widget.db:
                self.balance_widget.db.close()

            from app.utils.database import SessionLocal
            self.balance_widget.db = SessionLocal()
            self.balance_widget.service = CustomerService(self.balance_widget.db)
            self.balance_widget.refresh_balance()

    def show_new_payment_dialog(self):
        """Affiche la boîte de dialogue de nouveau paiement"""
        customer_id = self.customer_combo.currentData()
        if not customer_id:
            QMessageBox.warning(
                self,
                "Attention",
                "Veuillez sélectionner un client avant de créer un paiement."
            )
            return

        # Importer ici pour éviter les importations circulaires
        from app.ui.views.sale.dialogs.payment_dialog import PaymentDialog

        # Récupérer la vente sélectionnée si disponible
        selected_indexes = self.sales_table.selectedIndexes()
        sale_id = None

        if selected_indexes:
            row = self.sales_proxy_model.mapToSource(selected_indexes[0]).row()
            sale_id = self.sales_model.get_id_at_row(row)

        dialog = PaymentDialog(self, sale_id=sale_id, customer_id=customer_id)
        if dialog.exec():
            self._load_data_wrapper()

            # Rafraîchir explicitement le widget de solde
            print(f"Rafraîchissement du solde client après nouveau paiement pour le client {customer_id}")
            # Forcer la recréation de la session dans le widget de solde
            if hasattr(self.balance_widget, 'db') and self.balance_widget.db:
                self.balance_widget.db.close()

            from app.utils.database import SessionLocal
            self.balance_widget.db = SessionLocal()
            self.balance_widget.service = CustomerService(self.balance_widget.db)
            self.balance_widget.refresh_balance()

    def refresh_balance_widget(self):
        """Rafraîchit le widget de solde automatiquement"""
        customer_id = self.customer_combo.currentData()
        if customer_id:
            # Forcer la recréation de la session dans le widget de solde
            if hasattr(self.balance_widget, 'db') and self.balance_widget.db:
                self.balance_widget.db.close()

            from app.utils.database import SessionLocal
            self.balance_widget.db = SessionLocal()
            self.balance_widget.service = CustomerService(self.balance_widget.db)

            # Rafraîchir le solde
            self.balance_widget.refresh_balance()
            print(f"Widget de solde rafraîchi automatiquement pour le client {customer_id}")

    def filter_repairs(self):
        """Filtre la liste des réparations"""
        filter_text = self.repairs_filter.text()

        # Configurer le filtre de texte
        self.repairs_proxy_model.setFilterKeyColumn(-1)  # Rechercher dans toutes les colonnes
        self.repairs_proxy_model.setFilterFixedString(filter_text)

        # Filtrer par statut
        status = self.repair_status_filter.currentData()
        if status:
            self.repairs_model.filter_by_status(status)
        else:
            self.repairs_model.clear_status_filter()

    def show_repair_details(self):
        """Affiche les détails d'une réparation"""
        # Récupérer la réparation sélectionnée
        selected_indexes = self.repairs_table.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(
                self,
                "Attention",
                "Veuillez sélectionner une réparation."
            )
            return

        # Récupérer l'ID de la réparation
        row = self.repairs_proxy_model.mapToSource(selected_indexes[0]).row()
        repair_id = self.repairs_model.get_id_at_row(row)

        # Ouvrir la vue de réparation avec cette réparation
        from app.ui.views.repair.dialogs.repair_dialog import RepairDialog
        dialog = RepairDialog(self, repair_id=repair_id)
        dialog.exec()

        # Rafraîchir les données après la fermeture du dialogue
        self._load_data_wrapper()

    def show_invoice_dialog(self):
        """Affiche la boîte de dialogue de facturation"""
        # Récupérer la réparation sélectionnée
        selected_indexes = self.repairs_table.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(
                self,
                "Attention",
                "Veuillez sélectionner une réparation."
            )
            return

        # Récupérer l'ID de la réparation
        row = self.repairs_proxy_model.mapToSource(selected_indexes[0]).row()
        repair_id = self.repairs_model.get_id_at_row(row)

        # Ouvrir la boîte de dialogue de facturation
        from app.ui.views.repair.dialogs.invoice_dialog import InvoiceDialog
        dialog = InvoiceDialog(self, repair_id=repair_id)
        if dialog.exec():
            # Rafraîchir les données après la fermeture du dialogue
            self._load_data_wrapper()

            # Rafraîchir explicitement le widget de solde
            customer_id = self.customer_combo.currentData()
            if customer_id:
                print(f"Rafraîchissement du solde client après facturation de réparation pour le client {customer_id}")
                # Forcer la recréation de la session dans le widget de solde
                if hasattr(self.balance_widget, 'db') and self.balance_widget.db:
                    self.balance_widget.db.close()

                from app.utils.database import SessionLocal
                self.balance_widget.db = SessionLocal()
                self.balance_widget.service = CustomerService(self.balance_widget.db)
                self.balance_widget.refresh_balance()

    def show_repair_payment_dialog(self):
        """Affiche la boîte de dialogue de paiement"""
        # Récupérer la réparation sélectionnée
        selected_indexes = self.repairs_table.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(
                self,
                "Attention",
                "Veuillez sélectionner une réparation."
            )
            return

        # Récupérer l'ID de la réparation
        row = self.repairs_proxy_model.mapToSource(selected_indexes[0]).row()
        repair_id = self.repairs_model.get_id_at_row(row)

        # Ouvrir la boîte de dialogue de paiement
        from app.ui.views.repair.dialogs.payment_dialog import PaymentDialog
        dialog = PaymentDialog(self, repair_id=repair_id)
        if dialog.exec():
            # Rafraîchir les données après la fermeture du dialogue
            self._load_data_wrapper()

            # Rafraîchir explicitement le widget de solde
            customer_id = self.customer_combo.currentData()
            if customer_id:
                print(f"Rafraîchissement du solde client après paiement de réparation pour le client {customer_id}")
                # Forcer la recréation de la session dans le widget de solde
                if hasattr(self.balance_widget, 'db') and self.balance_widget.db:
                    self.balance_widget.db.close()

                from app.utils.database import SessionLocal
                self.balance_widget.db = SessionLocal()
                self.balance_widget.service = CustomerService(self.balance_widget.db)
                self.balance_widget.refresh_balance()

    def select_customer(self, customer_id):
        """Sélectionne un client spécifique"""
        # Trouver l'index du client dans le combo
        for i in range(self.customer_combo.count()):
            if self.customer_combo.itemData(i) == customer_id:
                self.customer_combo.setCurrentIndex(i)
                return

        # Si le client n'est pas trouvé, recharger les données
        print(f"Client {customer_id} non trouvé dans le combo, rechargement des données...")
        self._load_data_wrapper()
