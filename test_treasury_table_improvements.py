#!/usr/bin/env python3
"""
Script de test pour vérifier les améliorations du tableau des transactions récentes.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.ui.views.treasury.widgets.transaction_table_model import TransactionTableModel
from app.core.models.treasury import CashTransaction, TransactionCategory, PaymentMethod
from datetime import datetime
from PyQt6.QtWidgets import QTableView, QHeaderView

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des améliorations du tableau des transactions")
        self.setGeometry(100, 100, 1000, 600)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Créer le tableau avec les améliorations
        self.table = QTableView()
        self.model = TransactionTableModel()
        self.table.setModel(self.model)
        
        # Appliquer les améliorations comme dans treasury_view.py
        header = self.table.horizontalHeader()
        
        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        
        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)
        
        # Ajouter des lignes alternées pour une meilleure lisibilité
        self.table.setAlternatingRowColors(True)
        
        # Configurer la sélection par ligne complète
        self.table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        
        # Désactiver l'édition directe dans le tableau
        self.table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        
        # Améliorer l'apparence générale
        self.table.setShowGrid(True)
        self.table.setSortingEnabled(True)
        
        layout.addWidget(self.table)
        
        # Ajouter des données de test
        self.add_test_data()
    
    def add_test_data(self):
        """Ajoute des données de test au tableau"""
        # Créer des transactions de test
        test_transactions = []
        
        # Transaction 1 - Vente
        transaction1 = CashTransaction()
        transaction1.id = 1
        transaction1.transaction_date = datetime.now()
        transaction1.amount = 150.75
        transaction1.category = TransactionCategory.SALE
        transaction1.payment_method = PaymentMethod.cash
        transaction1.reference_number = "VTE-001"
        transaction1.description = "Vente de produits électroniques"
        test_transactions.append(transaction1)
        
        # Transaction 2 - Achat
        transaction2 = CashTransaction()
        transaction2.id = 2
        transaction2.transaction_date = datetime.now()
        transaction2.amount = -85.50
        transaction2.category = TransactionCategory.PURCHASE
        transaction2.payment_method = PaymentMethod.bank_transfer
        transaction2.reference_number = "ACH-002"
        transaction2.description = "Achat de matériel de bureau"
        test_transactions.append(transaction2)
        
        # Transaction 3 - Réparation
        transaction3 = CashTransaction()
        transaction3.id = 3
        transaction3.transaction_date = datetime.now()
        transaction3.amount = 75.00
        transaction3.category = TransactionCategory.REPAIR
        transaction3.payment_method = PaymentMethod.credit_card
        transaction3.reference_number = "REP-003"
        transaction3.description = "Réparation d'équipement informatique"
        test_transactions.append(transaction3)
        
        # Mettre à jour le modèle
        self.model.setTransactions(test_transactions)

def main():
    app = QApplication(sys.argv)
    
    # Créer et afficher la fenêtre de test
    window = TestWindow()
    window.show()
    
    print("Test des améliorations du tableau des transactions:")
    print("- Colonnes ajustées automatiquement selon le contenu")
    print("- Dernière colonne étirée pour utiliser tout l'espace")
    print("- Lignes alternées pour une meilleure lisibilité")
    print("- Sélection par ligne complète")
    print("- Édition désactivée")
    print("- Grille visible et tri activé")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
