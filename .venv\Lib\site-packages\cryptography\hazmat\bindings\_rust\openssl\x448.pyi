# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from cryptography.hazmat.primitives.asymmetric import x448
from cryptography.utils import Buffer

class X448PrivateKey: ...
class X448PublicKey: ...

def generate_key() -> x448.X448PrivateKey: ...
def from_private_bytes(data: Buffer) -> x448.X448PrivateKey: ...
def from_public_bytes(data: bytes) -> x448.X448PublicKey: ...
