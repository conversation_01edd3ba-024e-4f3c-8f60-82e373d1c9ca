#!/usr/bin/env python3
"""
Script de test pour vérifier les améliorations des tableaux dans le module clients.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTabWidget
from PyQt6.QtCore import Qt

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.ui.views.customer.customer_table_model import CustomerTableModel
from app.ui.models.customer_finance_table_model import CustomerTransactionTableModel, CustomerSaleTableModel
from app.ui.models.customer_repair_table_model import CustomerRepairTableModel
from app.core.models.customer import Customer
from datetime import datetime
from PyQt6.QtWidgets import QTableView, QHeaderView

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des améliorations des tableaux - Module Clients")
        self.setGeometry(100, 100, 1400, 800)
        
        # Widget central avec onglets
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Onglets pour tester différents tableaux
        tab_widget = QTabWidget()
        
        # Onglet 1: Tableau des clients
        clients_tab = self.create_clients_tab()
        tab_widget.addTab(clients_tab, "Clients")
        
        # Onglet 2: Tableau des transactions
        transactions_tab = self.create_transactions_tab()
        tab_widget.addTab(transactions_tab, "Transactions clients")
        
        # Onglet 3: Tableau des ventes
        sales_tab = self.create_sales_tab()
        tab_widget.addTab(sales_tab, "Ventes clients")
        
        # Onglet 4: Tableau des réparations
        repairs_tab = self.create_repairs_tab()
        tab_widget.addTab(repairs_tab, "Réparations clients")
        
        layout.addWidget(tab_widget)
        
        # Ajouter des données de test
        self.add_test_data()
    
    def create_clients_tab(self):
        """Crée l'onglet pour tester le tableau des clients"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Créer le tableau avec les améliorations
        self.clients_table = QTableView()
        self.clients_model = CustomerTableModel()
        self.clients_table.setModel(self.clients_model)
        
        # Appliquer les améliorations comme dans customer_view.py
        self.clients_table.setSortingEnabled(True)
        self.clients_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.clients_table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.clients_table.setAlternatingRowColors(True)
        
        header = self.clients_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)
        self.clients_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.clients_table.setShowGrid(True)
        
        layout.addWidget(self.clients_table)
        return widget
    
    def create_transactions_tab(self):
        """Crée l'onglet pour tester le tableau des transactions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Créer le tableau avec les améliorations
        self.transactions_table = QTableView()
        self.transactions_model = CustomerTransactionTableModel()
        self.transactions_table.setModel(self.transactions_model)
        
        # Appliquer les améliorations comme dans customer_finance_view.py
        self.transactions_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.transactions_table.setAlternatingRowColors(True)
        
        header = self.transactions_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)
        self.transactions_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.transactions_table.setShowGrid(True)
        self.transactions_table.setSortingEnabled(True)
        
        layout.addWidget(self.transactions_table)
        return widget
    
    def create_sales_tab(self):
        """Crée l'onglet pour tester le tableau des ventes"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Créer le tableau avec les améliorations
        self.sales_table = QTableView()
        self.sales_model = CustomerSaleTableModel()
        self.sales_table.setModel(self.sales_model)
        
        # Appliquer les améliorations comme dans customer_finance_view.py
        self.sales_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.sales_table.setAlternatingRowColors(True)
        
        header = self.sales_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)
        self.sales_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.sales_table.setShowGrid(True)
        self.sales_table.setSortingEnabled(True)
        
        layout.addWidget(self.sales_table)
        return widget
    
    def create_repairs_tab(self):
        """Crée l'onglet pour tester le tableau des réparations"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Créer le tableau avec les améliorations
        self.repairs_table = QTableView()
        self.repairs_model = CustomerRepairTableModel()
        self.repairs_table.setModel(self.repairs_model)
        
        # Appliquer les améliorations comme dans customer_finance_view.py
        self.repairs_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.repairs_table.setAlternatingRowColors(True)
        
        header = self.repairs_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)
        self.repairs_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.repairs_table.setShowGrid(True)
        self.repairs_table.setSortingEnabled(True)
        
        layout.addWidget(self.repairs_table)
        return widget
    
    def add_test_data(self):
        """Ajoute des données de test aux tableaux"""
        # Créer des clients de test
        test_customers = []
        
        # Client 1
        customer1 = Customer()
        customer1.id = 1
        customer1.name = "Entreprise Alpha SARL"
        customer1.contact_person = "Ahmed Benali"
        customer1.phone = "0555123456"
        customer1.email = "<EMAIL>"
        customer1.address = "123 Rue de la Liberté"
        customer1.commune = "Sidi M'Hamed"
        customer1.city = "Alger"
        customer1.credit_limit = 50000.00
        customer1.current_balance = 1250.75
        test_customers.append(customer1)
        
        # Client 2
        customer2 = Customer()
        customer2.id = 2
        customer2.name = "Boutique Beta"
        customer2.contact_person = "Fatima Khelil"
        customer2.phone = "0666789012"
        customer2.email = "<EMAIL>"
        customer2.address = "456 Avenue de l'Indépendance"
        customer2.commune = "El Madania"
        customer2.city = "Alger"
        customer2.credit_limit = 25000.00
        customer2.current_balance = 0.00
        test_customers.append(customer2)
        
        # Client 3
        customer3 = Customer()
        customer3.id = 3
        customer3.name = "Société Gamma Technologies"
        customer3.contact_person = "Karim Meziane"
        customer3.phone = "0777345678"
        customer3.email = "<EMAIL>"
        customer3.address = "789 Boulevard des Martyrs"
        customer3.commune = "Bab El Oued"
        customer3.city = "Alger"
        customer3.credit_limit = 100000.00
        customer3.current_balance = 5750.50
        test_customers.append(customer3)
        
        # Mettre à jour le modèle des clients
        self.clients_model.customers = test_customers
        self.clients_model.layoutChanged.emit()
        
        print("Données de test ajoutées pour les clients")

def main():
    app = QApplication(sys.argv)
    
    # Créer et afficher la fenêtre de test
    window = TestWindow()
    window.show()
    
    print("Test des améliorations des tableaux - Module Clients:")
    print("- Tableau principal des clients avec colonnes ajustées automatiquement")
    print("- Tableau des transactions clients avec dernière colonne étirée")
    print("- Tableau des ventes clients avec lignes alternées")
    print("- Tableau des réparations clients avec sélection par ligne complète")
    print("- Tous les tableaux : édition désactivée, tri activé, grille visible")
    print("- Utilisation optimale de l'espace disponible")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
