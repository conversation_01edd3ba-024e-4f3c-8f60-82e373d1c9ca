#!/usr/bin/env python3
"""
Script de test pour vérifier les améliorations des tableaux dans le module achats.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTabWidget
from PyQt6.QtCore import Qt

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.ui.views.purchasing.purchase_order_table_model import PurchaseOrderTableModel
from app.ui.views.purchasing.order_items_table_model import OrderItemsTableModel
from app.core.models.purchasing import PurchaseOrder, OrderStatus, PurchaseOrderItem
from app.core.models.inventory import Product
from app.core.models.supplier import Supplier
from datetime import datetime
from PyQt6.QtWidgets import QTableView, QHeaderView

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test des améliorations des tableaux - Module Achats")
        self.setGeometry(100, 100, 1200, 700)
        
        # Widget central avec onglets
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Onglets pour tester différents tableaux
        tab_widget = QTabWidget()
        
        # Onglet 1: Tableau des commandes
        orders_tab = self.create_orders_tab()
        tab_widget.addTab(orders_tab, "Commandes d'achat")
        
        # Onglet 2: Tableau des articles
        items_tab = self.create_items_tab()
        tab_widget.addTab(items_tab, "Articles de commande")
        
        layout.addWidget(tab_widget)
        
        # Ajouter des données de test
        self.add_test_data()
    
    def create_orders_tab(self):
        """Crée l'onglet pour tester le tableau des commandes"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Créer le tableau avec les améliorations
        self.orders_table = QTableView()
        self.orders_model = PurchaseOrderTableModel()
        self.orders_table.setModel(self.orders_model)
        
        # Appliquer les améliorations comme dans purchasing_view.py
        self.orders_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.orders_table.setAlternatingRowColors(True)
        
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)
        self.orders_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.orders_table.setShowGrid(True)
        self.orders_table.setSortingEnabled(True)
        
        layout.addWidget(self.orders_table)
        return widget
    
    def create_items_tab(self):
        """Crée l'onglet pour tester le tableau des articles"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Créer le tableau avec les améliorations
        self.items_table = QTableView()
        self.items_model = OrderItemsTableModel()
        self.items_table.setModel(self.items_model)
        
        # Appliquer les améliorations comme dans order_items_widget.py
        self.items_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setStretchLastSection(True)
        self.items_table.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)
        self.items_table.setShowGrid(True)
        self.items_table.setSortingEnabled(True)
        
        layout.addWidget(self.items_table)
        return widget
    
    def add_test_data(self):
        """Ajoute des données de test aux tableaux"""
        # Créer des commandes de test
        test_orders = []
        
        # Commande 1
        order1 = PurchaseOrder()
        order1.id = 1
        order1.po_number = "PO-2024-001"
        order1.status = OrderStatus.PENDING
        order1.order_date = datetime.now()
        order1.expected_delivery = datetime.now()
        order1.total_amount = 1250.75
        
        # Simuler un fournisseur
        supplier1 = Supplier()
        supplier1.name = "Fournisseur Électronique SA"
        order1.supplier = supplier1
        
        test_orders.append(order1)
        
        # Commande 2
        order2 = PurchaseOrder()
        order2.id = 2
        order2.po_number = "PO-2024-002"
        order2.status = OrderStatus.APPROVED
        order2.order_date = datetime.now()
        order2.expected_delivery = datetime.now()
        order2.total_amount = 850.50
        
        supplier2 = Supplier()
        supplier2.name = "Matériel Bureau Plus"
        order2.supplier = supplier2
        
        test_orders.append(order2)
        
        # Commande 3
        order3 = PurchaseOrder()
        order3.id = 3
        order3.po_number = "PO-2024-003"
        order3.status = OrderStatus.COMPLETED
        order3.order_date = datetime.now()
        order3.expected_delivery = datetime.now()
        order3.total_amount = 2100.00
        
        supplier3 = Supplier()
        supplier3.name = "Équipements Industriels SARL"
        order3.supplier = supplier3
        
        test_orders.append(order3)
        
        # Mettre à jour le modèle des commandes
        self.orders_model.orders = test_orders
        self.orders_model.layoutChanged.emit()
        
        # Créer des articles de test
        test_items = []
        
        # Article 1
        item1 = PurchaseOrderItem()
        item1.quantity = 10
        item1.purchase_unit_price = 45.50
        item1.received_quantity = 0
        item1.specifications = "Écrans 24 pouces Full HD"
        item1.delivery_date = datetime.now()
        
        product1 = Product()
        product1.name = "Écran LCD 24\""
        item1.product = product1
        
        test_items.append(item1)
        
        # Article 2
        item2 = PurchaseOrderItem()
        item2.quantity = 5
        item2.purchase_unit_price = 125.00
        item2.received_quantity = 2
        item2.specifications = "Claviers mécaniques RGB"
        item2.delivery_date = datetime.now()
        
        product2 = Product()
        product2.name = "Clavier Mécanique"
        item2.product = product2
        
        test_items.append(item2)
        
        # Article 3
        item3 = PurchaseOrderItem()
        item3.quantity = 20
        item3.purchase_unit_price = 15.75
        item3.received_quantity = 20
        item3.specifications = "Souris optiques sans fil"
        item3.delivery_date = datetime.now()
        
        product3 = Product()
        product3.name = "Souris Sans Fil"
        item3.product = product3
        
        test_items.append(item3)
        
        # Mettre à jour le modèle des articles
        self.items_model.set_items(test_items)

def main():
    app = QApplication(sys.argv)
    
    # Créer et afficher la fenêtre de test
    window = TestWindow()
    window.show()
    
    print("Test des améliorations des tableaux - Module Achats:")
    print("- Tableau des commandes d'achat avec colonnes ajustées automatiquement")
    print("- Tableau des articles de commande avec dernière colonne étirée")
    print("- Lignes alternées pour une meilleure lisibilité")
    print("- Sélection par ligne complète")
    print("- Édition désactivée et tri activé")
    print("- Grille visible pour une meilleure séparation")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
